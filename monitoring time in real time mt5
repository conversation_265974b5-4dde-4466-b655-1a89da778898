import MetaTrader5 as mt5
from datetime import datetime
import time
import os

class MT5TimeMonitor:
    def __init__(self, symbol="EURJPY"):
        self.symbol = symbol
        self.connected = False
        
    def connect(self):
        """Connect to MetaTrader 5."""
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            print(f"❌ Error: {mt5.last_error()}")
            return False
        self.connected = True
        print("✅ Connected to MT5")
        
        # Get account info to verify connection
        account_info = mt5.account_info()
        if account_info:
            print(f"✅ Account: {account_info.login}")
            print(f"✅ Server: {account_info.server}")
        
        return True
    
    def disconnect(self):
        """Disconnect from MetaTrader 5."""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            print("✅ Disconnected from MT5")
    
    def get_server_time(self):
        """Get MT5 server time with multiple fallback methods."""
        if not self.connected:
            return None
        
        try:
            # Method 1: Try to get tick data for the symbol
            tick = mt5.symbol_info_tick(self.symbol)
            if tick and tick.time > 0:
                return datetime.fromtimestamp(tick.time)
            
            # Method 2: Try different symbols as fallback
            fallback_symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
            for symbol in fallback_symbols:
                tick = mt5.symbol_info_tick(symbol)
                if tick and tick.time > 0:
                    return datetime.fromtimestamp(tick.time)
            
            # Method 3: Get market data for any available symbol
            symbols = mt5.symbols_get()
            if symbols:
                for symbol_info in symbols[:10]:  # Try first 10 symbols
                    tick = mt5.symbol_info_tick(symbol_info.name)
                    if tick and tick.time > 0:
                        return datetime.fromtimestamp(tick.time)
            
            return None
            
        except Exception as e:
            print(f"❌ Error getting server time: {e}")
            return None
    
    def run_monitor(self):
        """Run the time monitor."""
        print("\n" + "="*60)
        print("MT5 SERVER TIME MONITOR")
        print("="*60)
        print("Press Ctrl+C to stop")
        print("="*60)
        
        consecutive_failures = 0
        max_failures = 5
        
        try:
            while True:
                server_time = self.get_server_time()
                if server_time:
                    consecutive_failures = 0  # Reset failure counter
                    
                    # Clear the line and display current time
                    time_str = server_time.strftime("%Y-%m-%d %H:%M:%S")
                    date_str = server_time.strftime("%A, %B %d, %Y")
                    hour = server_time.hour
                    minute = server_time.minute
                    second = server_time.second
                    
                    # Clear screen and display time
                    os.system('cls' if os.name == 'nt' else 'clear')
                    print("\n" + "="*60)
                    print("MT5 SERVER TIME MONITOR")
                    print("="*60)
                    print(f"📅 Date: {date_str}")
                    print(f"⏰ Time: {time_str}")
                    print(f"🕐 Hour: {hour:02d}")
                    print(f"⏱️  Minute: {minute:02d}")
                    print(f"⏲️  Second: {second:02d}")
                    print("="*60)
                    print("Press Ctrl+C to stop")
                    
                    # Check for 5-minute candle boundaries
                    minutes_in_candle = minute % 5
                    if minutes_in_candle == 0 and second == 0:
                        print("🎯 NEW 5-MINUTE CANDLE STARTED!")
                    elif minutes_in_candle == 4 and second == 50:
                        print("⚡ PREDICTION TIME (4:50 mark)!")
                    
                else:
                    consecutive_failures += 1
                    print(f"❌ Failed to get server time (attempt {consecutive_failures}/{max_failures})")
                    
                    if consecutive_failures >= max_failures:
                        print("❌ Too many consecutive failures. Attempting to reconnect...")
                        self.disconnect()
                        time.sleep(2)
                        if not self.connect():
                            print("❌ Reconnection failed. Exiting...")
                            break
                        consecutive_failures = 0
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Time monitor stopped by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")
    
    def __del__(self):
        """Cleanup."""
        self.disconnect()

def main():
    """Main function."""
    print("Starting MT5 Time Monitor...")
    
    # Check if MT5 is installed
    try:
        import MetaTrader5 as mt5
    except ImportError:
        print("❌ MetaTrader5 package not found. Install it with: pip install MetaTrader5")
        return
    
    monitor = MT5TimeMonitor()
    if not monitor.connect():
        print("❌ Failed to connect to MT5. Please check:")
        print("   - MT5 terminal is running")
        print("   - You're logged into your trading account")
        print("   - Algorithm trading is enabled in MT5")
        print("   - Your firewall isn't blocking the connection")
        return
    
    try:
        monitor.run_monitor()
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    main()