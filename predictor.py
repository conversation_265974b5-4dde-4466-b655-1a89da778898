import os
import warnings
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import pickle
import json
import tensorflow as tf
from datetime import datetime, timedelta
import glob

# Suppress warnings and TensorFlow logs
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class SymbolicFeatureGenerator:
    def __init__(self):
        self.feature_recipes = []
        self.new_feature_names = []

    def _apply_operation(self, recipe, x, y=None, cond=None):
        try:
            operation = eval(recipe['operation_str'])
            if recipe.get('is_ternary', False):
                return operation(cond, x, y)
            elif recipe['is_binary']:
                return operation(x, y)
            else:
                return operation(x, None)
        except:
            return np.zeros_like(x) if hasattr(x, '__len__') else 0

    def transform(self, X):
        X_copy = X.copy()
        for recipe in self.feature_recipes:
            try:
                if recipe.get('is_ternary', False):
                    if all(f in X_copy.columns for f in [recipe['condition'], recipe['feature1'], recipe['feature2']]):
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']], X_copy[recipe['feature2']], X_copy[recipe['condition']])
                    else:
                        X_copy[recipe['name']] = 0
                elif recipe['is_binary']:
                    if recipe['feature1'] in X_copy.columns and recipe['feature2'] in X_copy.columns:
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']], X_copy[recipe['feature2']])
                    else:
                        X_copy[recipe['name']] = 0
                else:
                    if recipe['feature1'] in X_copy.columns:
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']])
                    else:
                        X_copy[recipe['name']] = 0
            except:
                X_copy[recipe['name']] = 0
        return X_copy.replace([np.inf, -np.inf], np.nan).fillna(0)

class FeatureEngineeringPipeline:
    def __init__(self):
        self.scaler = None
        self.selected_features = []
        self.symbolic_generator = None
        self.pipeline_config = {}

    def create_base_features(self, df):
        print("🔧 Creating base features...")
        data = df.copy()
        
        # Swing features
        for period in [20, 50, 100]:
            data[f'swing_high_{period}'] = data['high'].rolling(period).max()
            data[f'swing_low_{period}'] = data['low'].rolling(period).min()
            data[f'swing_range_{period}'] = data[f'swing_high_{period}'] - data[f'swing_low_{period}']
            
            # Fibonacci levels
            for level in [0.236, 0.382, 0.5, 0.618, 0.786]:
                level_str = int(level * 1000)
                data[f'fib_{level_str}_{period}'] = data[f'swing_high_{period}'] - level * data[f'swing_range_{period}']
                data[f'distance_to_fib_{level_str}_{period}'] = (data['close'] - data[f'fib_{level_str}_{period}']) / data['close']
                data[f'abs_distance_to_fib_{level_str}_{period}'] = abs(data[f'distance_to_fib_{level_str}_{period}'])

        # Confluence score
        data['fib_confluence_score'] = 0
        for period in [20, 50]:
            for level in [0.382, 0.5, 0.618]:
                level_str = int(level * 1000)
                data['fib_confluence_score'] += (data[f'abs_distance_to_fib_{level_str}_{period}'] < 0.001).astype(int)

        # Trend and momentum
        data['trend_strength_10'] = (data['close'] - data['close'].shift(10)) / data['close'].shift(10)
        data['trend_strength_20'] = (data['close'] - data['close'].shift(20)) / data['close'].shift(20)
        
        # Moving averages
        for period in [10, 20, 50]:
            data[f'sma_{period}'] = data['close'].rolling(period).mean()
            data[f'above_sma_{period}'] = (data['close'] > data[f'sma_{period}']).astype(int)
        
        data['sma_alignment_bullish'] = ((data['sma_10'] > data['sma_20']) & (data['sma_20'] > data['sma_50'])).astype(int)
        data['sma_alignment_bearish'] = ((data['sma_10'] < data['sma_20']) & (data['sma_20'] < data['sma_50'])).astype(int)

        # Momentum
        for period in [3, 5, 10]:
            data[f'momentum_{period}'] = data['close'] - data['close'].shift(period)
        data['momentum_decreasing'] = (abs(data['momentum_3']) < abs(data['momentum_5'])).astype(int)

        # Volatility
        data['atr_14'] = (data['high'] - data['low']).rolling(14).mean()
        data['current_range'] = data['high'] - data['low']
        data['volatility_ratio'] = data['current_range'] / data['atr_14']
        data['recent_volatility_expansion'] = (data['volatility_ratio'].rolling(3).max() > 1.5).astype(int)

        # Candlestick patterns
        data['body_size'] = abs(data['close'] - data['open'])
        data['upper_shadow'] = data['high'] - data[['open', 'close']].max(axis=1)
        data['lower_shadow'] = data[['open', 'close']].min(axis=1) - data['low']
        data['total_range'] = data['high'] - data['low']
        data['hammer_pattern'] = ((data['lower_shadow'] > data['body_size'] * 2) & (data['upper_shadow'] < data['body_size'] * 0.5)).astype(int)
        data['doji_pattern'] = (data['body_size'] / data['total_range'] < 0.1).astype(int)

        # Trading sessions
        data['hour'] = data.index.hour
        data['london_session'] = data['hour'].between(8, 16).astype(int)
        data['ny_session'] = data['hour'].between(13, 21).astype(int)
        data['session_overlap'] = (data['london_session'] & data['ny_session']).astype(int)

        # Volume features
        if 'volume' in data.columns:
            data['volume_ma_20'] = data['volume'].rolling(20).mean()
            data['volume_ratio'] = data['volume'] / data['volume_ma_20']
            data['volume_confirmation'] = (data['volume_ratio'] > 1.2).astype(int)

        return data.replace([np.inf, -np.inf], np.nan).fillna(method='ffill').fillna(0)

    def transform(self, X):
        if self.scaler is None:
            raise ValueError("Pipeline not fitted. Load the pipeline first.")
        
        feature_columns_to_exclude = ['target', 'next_fib_bounce', 'future_open', 'timestamp', 'timestamp_original']
        
        if 'feature_columns_used_for_scaling' in self.pipeline_config:
            feature_cols = [col for col in self.pipeline_config['feature_columns_used_for_scaling'] if col in X.columns]
        else:
            feature_cols = [col for col in X.columns if col not in feature_columns_to_exclude and X[col].dtype in [np.float64, np.int64]]

        X_scaled = pd.DataFrame(self.scaler.transform(X[feature_cols]), columns=feature_cols, index=X.index)
        X_with_symbolic = self.symbolic_generator.transform(X_scaled)

        for feature in self.selected_features:
            if feature not in X_with_symbolic.columns:
                X_with_symbolic[feature] = 0

        return X_with_symbolic[self.selected_features]

    @classmethod
    def load(cls, filepath):
        print(f"📥 Loading pipeline from {filepath}")
        with open(filepath, 'rb') as f:
            pipeline_data = pickle.load(f)

        pipeline = cls()
        pipeline.scaler = pipeline_data['scaler']
        pipeline.selected_features = pipeline_data['selected_features']
        pipeline.symbolic_generator = pipeline_data['symbolic_generator']
        pipeline.pipeline_config = pipeline_data['pipeline_config']
        print(f"✅ Pipeline loaded with {len(pipeline.selected_features)} features")
        return pipeline

class FibonacciBouncePredictor:
    def __init__(self):
        self.model = None
        self.pipeline = None
        self.selected_features = None
        self.symbol = "EURJPY"
        self._auto_detect_files()

    def _auto_detect_files(self):
        current_dir = os.getcwd()
        
        # Find model files
        model_files = []
        for ext in ['*.keras', '*.h5']:
            model_files.extend(glob.glob(os.path.join(current_dir, ext)))
        model_files.extend(glob.glob(os.path.join(current_dir, '*model*')))
        
        # Find pipeline and feature files
        pipeline_files = glob.glob(os.path.join(current_dir, '*.pkl'))
        feature_files = glob.glob(os.path.join(current_dir, '*.json'))
        
        self.model_path = model_files[0] if model_files else None
        self.pipeline_path = pipeline_files[0] if pipeline_files else None
        self.features_path = feature_files[0] if feature_files else None
        
        print(f"🎯 Auto-detected files:")
        print(f"   Model: {os.path.basename(self.model_path) if self.model_path else 'None'}")
        print(f"   Pipeline: {os.path.basename(self.pipeline_path) if self.pipeline_path else 'None'}")
        print(f"   Features: {os.path.basename(self.features_path) if self.features_path else 'None'}")

    def load_model_and_pipeline(self):
        try:
            print("\n🚀 Loading model and pipeline...")
            
            # Load model
            if not self.model_path or not os.path.exists(self.model_path):
                print(f"❌ Model file not found: {self.model_path}")
                return False
            
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"✅ Model loaded: {os.path.basename(self.model_path)}")
            
            # Load pipeline
            if not self.pipeline_path or not os.path.exists(self.pipeline_path):
                print(f"❌ Pipeline file not found: {self.pipeline_path}")
                return False
            
            self.pipeline = FeatureEngineeringPipeline.load(self.pipeline_path)
            print(f"✅ Pipeline loaded: {os.path.basename(self.pipeline_path)}")
            
            # Load features
            if self.features_path and os.path.exists(self.features_path):
                with open(self.features_path, 'r') as f:
                    self.selected_features = json.load(f)
                self.pipeline.selected_features = self.selected_features
                print(f"✅ Features loaded: {len(self.selected_features)} features")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading model and pipeline: {e}")
            return False

    def connect_mt5(self):
        print("🔗 Connecting to MetaTrader 5...")
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return False
        
        # Test connection with symbol info
        symbol_info = mt5.symbol_info(self.symbol)
        if symbol_info is None:
            print(f"❌ Symbol {self.symbol} not found, trying EURUSD...")
            self.symbol = "EURUSD"
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                print("❌ No valid symbols found")
                return False
        
        print(f"✅ Connected to MT5 with symbol: {self.symbol}")
        return True

    def get_data(self, count=500):
        try:
            print(f"📊 Retrieving {count} candles for {self.symbol}...")
            
            # Try to get rates from current position
            rates = mt5.copy_rates_from_pos(self.symbol, mt5.TIMEFRAME_M5, 1, count)
            
            if rates is None or len(rates) == 0:
                print("❌ No data from current position, trying from datetime...")
                # Try with a specific datetime
                utc_to = datetime.now()
                rates = mt5.copy_rates_from(self.symbol, mt5.TIMEFRAME_M5, utc_to, count)
            
            if rates is None or len(rates) == 0:
                print("❌ No data retrieved")
                return None
            
            df = pd.DataFrame(rates)
            df['timestamp'] = pd.to_datetime(df['time'], unit='s')
            df = df.set_index('timestamp')
            df = df.rename(columns={'tick_volume': 'volume'})
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            print(f"✅ Retrieved {len(df)} candles, last: {df.index[-1]}")
            return df
            
        except Exception as e:
            print(f"❌ Error getting data: {e}")
            return None

    def make_prediction(self):
        try:
            print("\n🎯 Making prediction...")
            
            # Get data
            data = self.get_data()
            if data is None:
                return {"error": "Failed to get market data"}
            
            # Create and transform features
            print("🔧 Processing features...")
            feature_data = self.pipeline.create_base_features(data)
            X_pred = self.pipeline.transform(feature_data.iloc[[-1]])
            
            # Make prediction
            print("🤖 Running prediction...")
            prediction_prob = self.model.predict(X_pred.values, verbose=0)[0][0]
            prediction_class = 1 if prediction_prob > 0.5 else 0
            
            # Get current info
            last_candle = data.iloc[-1]
            current_time = datetime.now()
            
            result = {
                "prediction_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                "symbol": self.symbol,
                "prediction_class": prediction_class,
                "prediction_probability": float(prediction_prob),
                "current_price": float(last_candle['close']),
                "last_candle_time": last_candle.name.strftime("%Y-%m-%d %H:%M:%S"),
                "signal": "BUY" if prediction_class == 1 else "SELL"
            }
            
            print("✅ Prediction completed!")
            return result
            
        except Exception as e:
            print(f"❌ Prediction error: {e}")
            return {"error": f"Prediction failed: {str(e)}"}

    def display_result(self, result):
        if "error" in result:
            print(f"\n❌ ERROR: {result['error']}")
            return

        print(f"\n{'='*60}")
        print(f"🎯 FIBONACCI BOUNCE PREDICTION")
        print(f"{'='*60}")
        
        signal_emoji = "🟢" if result['signal'] == "BUY" else "🔴"
        prob = result['prediction_probability']
        confidence = "HIGH" if abs(prob - 0.5) > 0.3 else "MEDIUM" if abs(prob - 0.5) > 0.1 else "LOW"
        
        print(f"{signal_emoji} SIGNAL: {result['signal']}")
        print(f"📊 Probability: {prob:.1%}")
        print(f"🎯 Confidence: {confidence}")
        print(f"💹 Symbol: {result['symbol']}")
        print(f"💰 Current Price: {result['current_price']:.5f}")
        print(f"📅 Prediction Time: {result['prediction_time']}")
        print(f"📈 Last Candle: {result['last_candle_time']}")
        print(f"{'='*60}")

    def run(self):
        print("="*60)
        print("🚀 FIBONACCI BOUNCE PREDICTOR")
        print("="*60)
        
        try:
            # Load model and pipeline
            if not self.load_model_and_pipeline():
                print("❌ Failed to load model/pipeline")
                return
            
            # Connect to MT5
            if not self.connect_mt5():
                print("❌ Failed to connect to MT5")
                return
            
            # Make prediction
            result = self.make_prediction()
            self.display_result(result)
            
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            mt5.shutdown()
            print("🔌 MT5 disconnected")

if __name__ == "__main__":
    predictor = FibonacciBouncePredictor()
    predictor.run()