import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime
import time

# Initialize connection to MT5
if not mt5.initialize():
    print("Failed to connect to MT5")
    mt5.shutdown()
    exit()

# Function to fetch the latest complete candle
def get_latest_complete_candle(symbol, timeframe, delay_seconds=2):
    # Wait a few seconds to ensure the candle is complete
    time.sleep(delay_seconds)
    
    # Get the current UTC time (MT5 uses UTC)
    now = datetime.utcnow()
    
    # Calculate the start time of the latest complete 5-minute candle
    minutes = now.minute // 5 * 5  # Align to the last 5-minute mark
    start_time = datetime(now.year, now.month, now.day, now.hour, minutes)
    
    # Fetch the latest complete candle
    rates = mt5.copy_rates_from(symbol, timeframe, start_time, 1)
    
    if rates is None or len(rates) == 0:
        print("No data retrieved")
        return None
    
    # Convert to DataFrame for easy reading
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df

# Example usage
symbol = "EURUSD"  # Replace with your forex pair
timeframe = mt5.TIMEFRAME_M5  # 5-minute candles
delay_seconds = 2  # Wait 2 seconds after the candle closes

# Fetch the latest complete candle
latest_candle = get_latest_complete_candle(symbol, timeframe, delay_seconds)

if latest_candle is not None:
    print("Latest complete candle for", symbol)
    print(latest_candle)

# Disconnect from MT5
mt5.shutdown()
